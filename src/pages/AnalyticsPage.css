.analytics-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: #ffffff;
  font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
  overflow: hidden;
}

.page-header {
  background: rgba(15, 15, 35, 0.95);
  backdrop-filter: blur(20px);
  padding: 2rem;
  border-bottom: 1px solid rgba(120, 219, 255, 0.2);
  text-align: center;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #78dbff 0%, #ff77c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header p {
  margin: 0;
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.analytics-content {
  flex: 1;
  overflow: hidden;
  padding: 1rem;
}
