.event-setup-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 20px;
    font-family:
        -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
    overflow-y: visible;
    overflow-x: hidden;
}

.event-setup-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: visible;
    animation: slideUp 0.5s ease-out;
    position: relative;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.setup-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    text-align: center;
}

.setup-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.setup-header p {
    margin: 0 0 20px 0;
    font-size: 1.2rem;
    opacity: 0.9;
}

.user-welcome {
    background: rgba(255, 255, 255, 0.1);
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    display: inline-block;
    backdrop-filter: blur(10px);
}

.progress-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e2e8f0;
    color: #718096;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step-label {
    font-size: 0.9rem;
    color: #718096;
    font-weight: 500;
    transition: all 0.3s ease;
}

.progress-step.active .step-number {
    background: #667eea;
    color: white;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
}

.progress-step.active .step-label {
    color: #2d3748;
    font-weight: 600;
}

.progress-step.completed .step-number {
    background: #48bb78;
    color: white;
}

.progress-step.completed .step-label {
    color: #2d3748;
}

.progress-line {
    width: 100px;
    height: 2px;
    background: #e2e8f0;
    margin: 0 20px;
    position: relative;
    top: -20px;
}

.setup-content {
    padding: 40px;
}

.step-content h2 {
    margin: 0 0 8px 0;
    color: #2d3748;
    font-size: 1.8rem;
    font-weight: 700;
}

.step-content p {
    margin: 0 0 30px 0;
    color: #718096;
    font-size: 1.1rem;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-input {
    width: 100%;
    padding: 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
    background-color: #f7fafc;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.event-preview,
.location-preview {
    margin-top: 30px;
    padding: 20px;
    background: #f7fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.event-preview h3,
.location-preview h3 {
    margin: 0 0 16px 0;
    color: #2d3748;
    font-size: 1.2rem;
    font-weight: 600;
}

.preview-card {
    background: white;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    align-items: center;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.preview-icon {
    font-size: 2rem;
    margin-right: 16px;
    opacity: 0.8;
}

.preview-details {
    flex: 1;
}

.preview-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 1.1rem;
    margin-bottom: 4px;
}

.preview-meta,
.preview-address {
    color: #718096;
    font-size: 0.9rem;
    margin-bottom: 2px;
}

.preview-coordinates {
    color: #4a5568;
    font-size: 0.8rem;
    font-family: monospace;
}

.location-method-selector {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.method-btn {
    padding: 20px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.method-btn:hover {
    border-color: #cbd5e0;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.method-btn.active {
    border-color: #667eea;
    background: #f7fafc;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.method-icon {
    font-size: 2rem;
    margin-bottom: 8px;
    display: block;
}

.method-label {
    font-weight: 600;
    color: #2d3748;
    font-size: 1rem;
    margin-bottom: 4px;
    display: block;
}

.method-desc {
    color: #718096;
    font-size: 0.85rem;
    display: block;
}

.manual-location-form {
    background: #f7fafc;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.map-location-selector {
    background: #f7fafc;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.map-instructions {
    margin-bottom: 20px;
    text-align: center;
}

.map-instructions p {
    margin: 0 0 10px 0;
    color: #4a5568;
    font-weight: 500;
}

.selected-location-info {
    background: #e6fffa;
    color: #2d7738;
    padding: 12px;
    border-radius: 8px;
    font-size: 0.9rem;
    border: 1px solid #9ae6b4;
}

.map-container {
    margin: 20px 0;
    border-radius: 12px;
    overflow: hidden;
    border: 2px solid #e2e8f0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.google-map {
    width: 100%;
    height: 400px;
    background: #f7fafc;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #718096;
    font-size: 1rem;
}

.location-details-form {
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.error-message {
    display: flex;
    align-items: center;
    background-color: #fed7d7;
    color: #c53030;
    padding: 16px 20px;
    margin: 20px 40px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
}

.error-icon {
    margin-right: 8px;
    font-size: 1.1rem;
}

.setup-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 40px;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.nav-button {
    padding: 16px 32px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    display: flex;
    align-items: center;
    min-width: 140px;
    justify-content: center;
}

.nav-button.secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.nav-button.secondary:hover {
    background: #cbd5e0;
    transform: translateY(-1px);
}

.nav-button.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-left: auto;
}

.nav-button.primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.nav-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.nav-button.loading {
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .event-setup-page {
        padding: 10px;
        min-height: 100vh;
    }

    .event-setup-container {
        margin: 0 auto;
    }

    .setup-header {
        padding: 30px 20px;
    }

    .setup-header h1 {
        font-size: 2rem;
    }

    .progress-indicator {
        padding: 30px 20px;
    }

    .progress-line {
        width: 60px;
        margin: 0 10px;
    }

    .setup-content {
        padding: 30px 20px;
    }

    .location-method-selector {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .setup-navigation {
        padding: 20px;
        flex-direction: column;
        gap: 15px;
    }

    .nav-button {
        width: 100%;
    }

    .nav-button.primary {
        margin-left: 0;
    }
}

@media (max-width: 480px) {
    .event-setup-page {
        padding: 5px;
    }

    .event-setup-container {
        margin: 0 auto;
    }

    .setup-header h1 {
        font-size: 1.8rem;
    }

    .setup-content {
        padding: 20px 15px;
    }

    .google-map {
        height: 300px;
    }

    .method-btn {
        padding: 15px;
    }

    .method-icon {
        font-size: 1.5rem;
    }
}

/* Loading states */
.map-container.loading {
    background: #f7fafc;
    position: relative;
}

.map-container.loading::before {
    content: "Loading map...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #718096;
    font-size: 1rem;
    z-index: 10;
}

/* Animation for form transitions */
.step-content {
    animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
