.login-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40px 20px;
    font-family:
        -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
    overflow-y: visible;
    overflow-x: hidden;
}

.login-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 500px;
    animation: slideUp 0.5s ease-out;
    margin: 0 auto;
    position: relative;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    margin-bottom: 40px;
}

.login-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0 10px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-header p {
    color: #718096;
    font-size: 1.1rem;
    margin: 0;
    font-weight: 500;
}

.login-form {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-group input {
    width: 100%;
    padding: 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
    background-color: #f7fafc;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input:disabled {
    background-color: #f1f5f9;
    cursor: not-allowed;
    opacity: 0.7;
}

.error-message {
    display: flex;
    align-items: center;
    background-color: #fed7d7;
    color: #c53030;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.error-icon {
    margin-right: 8px;
    font-size: 1.1rem;
}

.login-button {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.login-button:active {
    transform: translateY(0);
}

.login-button:disabled {
    cursor: not-allowed;
    opacity: 0.7;
}

.login-button.loading {
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.test-credentials-section {
    border-top: 1px solid #e2e8f0;
    padding-top: 30px;
    margin-bottom: 30px;
}

.show-credentials-btn {
    width: 100%;
    padding: 12px;
    background-color: #f7fafc;
    color: #4a5568;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.show-credentials-btn:hover {
    background-color: #edf2f7;
    border-color: #cbd5e0;
}

.test-credentials {
    margin-top: 20px;
    padding: 20px;
    background-color: #f7fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.test-credentials h3 {
    margin: 0 0 20px 0;
    color: #2d3748;
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
}

.credentials-grid {
    display: grid;
    gap: 16px;
}

.credential-card {
    background: white;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.credential-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.credential-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.credential-header h4 {
    margin: 0;
    color: #2d3748;
    font-size: 1rem;
    font-weight: 600;
}

.role-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.role-badge.admin {
    background-color: #fed7d7;
    color: #c53030;
}

.role-badge.user {
    background-color: #bee3f8;
    color: #2b6cb0;
}

.credential-details {
    margin-bottom: 12px;
}

.credential-details p {
    margin: 4px 0;
    font-size: 0.9rem;
    color: #4a5568;
}

.credential-details strong {
    color: #2d3748;
    font-weight: 600;
}

.use-credential-btn {
    width: 100%;
    padding: 8px 12px;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.use-credential-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(72, 187, 120, 0.3);
}

.login-footer {
    text-align: center;
    border-top: 1px solid #e2e8f0;
    padding-top: 20px;
}

.login-footer p {
    margin: 0;
    color: #718096;
    font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-page {
        padding: 20px 10px;
        min-height: 100vh;
    }

    .login-container {
        padding: 30px 20px;
        max-width: 400px;
    }

    .login-header h1 {
        font-size: 2rem;
    }

    .credentials-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .login-page {
        padding: 10px 5px;
    }

    .login-container {
        padding: 20px 15px;
    }

    .login-header h1 {
        font-size: 1.8rem;
    }

    .form-group input,
    .login-button {
        padding: 14px;
    }
}
