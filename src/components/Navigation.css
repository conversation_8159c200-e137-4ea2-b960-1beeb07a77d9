/* Navigation Styles */
.dashboard-navigation {
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(120, 219, 255, 0.2);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-width: 280px;
    max-width: 320px;
}

.nav-header {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-header h2 {
    margin: 0 0 8px 0;
    font-size: 1.75rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-header p {
    margin: 0 0 20px 0;
    color: #a0aec0;
    font-size: 0.875rem;
    font-weight: 500;
}

/* User Info Styles */
.user-info {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
    backdrop-filter: blur(10px);
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1rem;
    margin-right: 12px;
    color: white;
}

.user-details {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
    color: white;
    margin-bottom: 2px;
}

.user-role {
    font-size: 0.75rem;
    color: #a0aec0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Event Info Styles */
.event-info {
    background: rgba(255, 255, 255, 0.05);
    padding: 14px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.event-info h3 {
    margin: 0 0 10px 0;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #a0aec0;
    font-weight: 600;
}

.event-details {
    display: flex;
    flex-direction: column;
}

.event-name {
    font-weight: 600;
    font-size: 0.95rem;
    color: white;
    margin-bottom: 4px;
    line-height: 1.3;
}

.event-location {
    font-size: 0.8rem;
    color: #cbd5e0;
    line-height: 1.3;
    opacity: 0.9;
}

.nav-items {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.nav-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(120, 219, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 1rem;
    text-align: left;
    color: white;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.nav-item::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    transition: left 0.5s;
}

.nav-item:hover::before {
    left: 100%;
}

.nav-item:hover {
    background: rgba(120, 219, 255, 0.1);
    border-color: rgba(120, 219, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(120, 219, 255, 0.2);
}

.nav-item.active {
    background: linear-gradient(
        135deg,
        rgba(120, 219, 255, 0.2) 0%,
        rgba(255, 119, 198, 0.1) 100%
    );
    border-color: #78dbff;
    box-shadow: 0 0 20px rgba(120, 219, 255, 0.3);
}

.nav-icon {
    font-size: 1.5rem;
    min-width: 2rem;
    text-align: center;
}

.nav-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex: 1;
}

.nav-label {
    font-size: 1rem;
    font-weight: 600;
    color: #ffffff;
}

.nav-description {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    line-height: 1.2;
}

.nav-footer {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(120, 219, 255, 0.2);
}

.system-status {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: statusPulse 2s infinite;
}

.status-dot.live {
    background: #00ff88;
    box-shadow: 0 0 10px #00ff88;
}

@keyframes statusPulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-navigation {
        min-width: 260px;
        max-width: 300px;
    }
}

@media (max-width: 1024px) {
    .dashboard-navigation {
        min-width: 240px;
        max-width: 260px;
    }

    .nav-header h2 {
        font-size: 1.6rem;
    }

    .nav-item {
        padding: 0.875rem;
    }
}

@media (max-width: 768px) {
    .app-layout {
        flex-direction: column;
    }

    .dashboard-navigation {
        min-width: 100%;
        max-width: 100%;
        height: auto;
        max-height: 200px;
        padding: 1rem;
        border-right: none;
        border-bottom: 1px solid rgba(120, 219, 255, 0.2);
    }

    .nav-header {
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
    }

    .nav-header h2 {
        font-size: 1.4rem;
    }

    .nav-header p {
        font-size: 0.8rem;
    }

    .nav-items {
        flex-direction: row;
        overflow-x: auto;
        gap: 0.5rem;
        padding-bottom: 0.5rem;
    }

    .nav-item {
        padding: 0.75rem;
        min-width: 140px;
        flex-shrink: 0;
    }

    .nav-icon {
        font-size: 1.2rem;
        min-width: 1.5rem;
    }

    .nav-label {
        font-size: 0.85rem;
    }

    .nav-description {
        font-size: 0.7rem;
        display: none;
    }

    .nav-footer {
        display: none;
    }
}

@media (max-width: 480px) {
    .dashboard-navigation {
        padding: 0.75rem;
    }

    .nav-header {
        margin-bottom: 0.75rem;
    }

    .nav-item {
        padding: 0.5rem;
        min-width: 120px;
    }

    .nav-icon {
        font-size: 1rem;
    }

    .nav-label {
        font-size: 0.8rem;
    }
}

/* Logout Button Styles */
.logout-button {
    width: 100%;
    background: rgba(255, 75, 75, 0.1);
    border: 1px solid rgba(255, 75, 75, 0.3);
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #ff6b6b;
    font-weight: 500;
    font-size: 0.9rem;
    margin-top: 12px;
}

.logout-button:hover {
    background: rgba(255, 75, 75, 0.2);
    border-color: rgba(255, 75, 75, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 75, 75, 0.2);
}

.logout-icon {
    font-size: 1rem;
}

@media (max-width: 768px) {
    .logout-button {
        display: none;
    }
}
