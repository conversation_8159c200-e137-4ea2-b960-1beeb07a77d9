/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.App {
    min-height: 100vh;
}

.app-full-page {
    height: auto;
    overflow-y: auto;
}

.app-dashboard {
    height: 100vh;
    overflow: hidden;
}

.app-layout {
    display: flex;
    height: 100vh;
    width: 100vw;
}

.app-main {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-layout {
        flex-direction: column;
    }

    .app-main {
        flex: 1;
        height: calc(100vh - 200px);
    }
}

body {
    margin: 0;
    font-family:
        -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
        "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: #1a1a1a;
    color: #ffffff;
    overflow-x: hidden;
}

html {
    scroll-behavior: smooth;
}

code {
    font-family:
        source-code-pro, Menlo, Monaco, Consolas, "Courier New", monospace;
}

/* Loading Screen Styles */
.loading-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.loading-content {
    text-align: center;
    animation: fadeInUp 0.6s ease-out;
}

.loading-content p {
    margin-top: 20px;
    font-size: 1.1rem;
    font-weight: 500;
    opacity: 0.9;
}

.spinner-large {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin: 0 auto;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
