# Drishti AI Crowd Detection - Environment Configuration Template
# Copy this file to .env and fill in your values

# ===================================
# Firebase Configuration
# ===================================
# Your Firebase project ID (found in Firebase Console)
REACT_APP_FIREBASE_PROJECT_ID=your-firebase-project-id

# ===================================
# Google Cloud Configuration
# ===================================
# Your Google Cloud Project ID (usually same as Firebase project ID)
REACT_APP_GOOGLE_CLOUD_PROJECT_ID=your-gcp-project-id

# Vertex AI / Google Cloud Vision API location
REACT_APP_VERTEX_LOCATION=us-central1

# ===================================
# Google Maps Configuration
# ===================================
# Google Maps API Key (for location picker and map display)
# Get this from Google Cloud Console > APIs & Services > Credentials
REACT_APP_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# ===================================
# Development Configuration
# ===================================
# Environment (development, production)
NODE_ENV=development

# Enable source maps in development
GENERATE_SOURCEMAP=true

# Disable CI mode warnings
CI=false

# ===================================
# API Configuration
# ===================================
# Backend API URL (automatically configured for Firebase Functions)
# In production: uses Firebase Hosting rewrites (/api/*)
# In development: uses Firebase emulator
# REACT_APP_API_URL is set automatically by the app

# ===================================
# Optional: Advanced Configuration
# ===================================
# Custom detection confidence threshold (0.0 - 1.0)
REACT_APP_CONFIDENCE_THRESHOLD=0.5

# Detection interval in milliseconds
REACT_APP_DETECTION_INTERVAL=500

# Maximum number of detections to process
REACT_APP_MAX_DETECTIONS=100

# Enable mock detection mode (useful for development without GCP setup)
REACT_APP_USE_MOCK_DETECTION=false

# ===================================
# Notes
# ===================================
# 1. Firebase Functions automatically handle authentication in production
# 2. No service account keys needed in this file - they're managed by Firebase
# 3. Local development uses Firebase emulators
# 4. Environment variables prefixed with REACT_APP_ are available in the frontend
# 5. Google Maps API key is required for location picker functionality
